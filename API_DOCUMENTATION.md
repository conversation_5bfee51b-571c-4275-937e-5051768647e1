# PDF Compression Service API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
No authentication required for this version.

## Content Types
- Request: `multipart/form-data` for file uploads, `application/json` for other requests
- Response: `application/json`

## Error Handling

All endpoints return standard HTTP status codes:
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (job/file not found)
- `413`: Payload Too Large (file too big)
- `422`: Validation Error
- `500`: Internal Server Error

Error response format:
```json
{
  "detail": "Error message description"
}
```

## Endpoints

### 1. Compress PDF

**Endpoint:** `POST /compress`

**Description:** Upload a PDF file and start compression job.

**Request Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| file | File | Yes | - | PDF file to compress |
| image_quality | int | No | 75 | JPEG quality (1-100) |
| image_scale | float | No | 0.8 | Scale factor for large images (0.1-1.0) |
| skip_pages | string | No | null | Pages to skip (e.g., "1,5,10-12") |
| method | string | No | "auto" | Compression method |
| target_size_mb | float | No | null | Target file size in MB |

**Compression Methods:**
- `auto`: Automatically select best method
- `pymupdf`: Use PyMuPDF library
- `ghostscript`: Use Ghostscript
- `pdf2image`: Convert to images and back

**Skip Pages Format:**
- Single pages: `"1,5,10"`
- Ranges: `"10-15"` (inclusive)
- Mixed: `"1,5,10-15,20"`

**Example Request:**
```bash
curl -X POST "http://localhost:8000/compress" \
  -F "file=@document.pdf" \
  -F "image_quality=80" \
  -F "skip_pages=1,10-12" \
  -F "method=auto" \
  -F "target_size_mb=5.0"
```

**Response:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "pending",
  "message": "PDF compression job started"
}
```

### 2. Get Job Status

**Endpoint:** `GET /status/{job_id}`

**Description:** Get real-time status and progress of a compression job.

**Path Parameters:**
- `job_id`: UUID of the compression job

**Example Request:**
```bash
curl "http://localhost:8000/status/550e8400-e29b-41d4-a716-************"
```

**Response (Processing):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "processing",
  "progress": 65.5,
  "current_page": 33,
  "total_pages": 50,
  "message": "Processing page 33/50",
  "download_url": null,
  "original_size_mb": 15.2,
  "compressed_size_mb": null,
  "compression_ratio": null,
  "error_details": null
}
```

**Response (Completed):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "progress": 100.0,
  "current_page": 50,
  "total_pages": 50,
  "message": "Compression completed successfully",
  "download_url": "http://localhost:8000/download/abc123-def456",
  "original_size_mb": 15.2,
  "compressed_size_mb": 4.8,
  "compression_ratio": 68.4,
  "error_details": null
}
```

**Response (Failed):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "failed",
  "progress": 0.0,
  "current_page": null,
  "total_pages": null,
  "message": "Compression failed",
  "download_url": null,
  "original_size_mb": 15.2,
  "compressed_size_mb": null,
  "compression_ratio": null,
  "error_details": "Invalid PDF file format"
}
```

**Status Values:**
- `pending`: Job created, waiting to start
- `processing`: Currently compressing the PDF
- `completed`: Compression finished successfully
- `failed`: Compression failed with error

### 3. Download Compressed File

**Endpoint:** `GET /download/{file_id}`

**Description:** Download the compressed PDF file.

**Path Parameters:**
- `file_id`: File ID from the download_url in status response

**Example Request:**
```bash
curl -O "http://localhost:8000/download/abc123-def456"
```

**Response:** Binary PDF file with appropriate headers:
- `Content-Type: application/pdf`
- `Content-Disposition: attachment; filename="document_compressed.pdf"`

### 4. Health Check

**Endpoint:** `GET /health`

**Description:** Check service health and dependency availability.

**Example Request:**
```bash
curl "http://localhost:8000/health"
```

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "dependencies": {
    "pymupdf": "available",
    "ghostscript": "available",
    "pdf2image": "available"
  }
}
```

## Usage Patterns

### 1. Simple Compression

```javascript
// Upload file
const formData = new FormData();
formData.append('file', pdfFile);

const uploadResponse = await fetch('/compress', {
  method: 'POST',
  body: formData
});

const { job_id } = await uploadResponse.json();

// Poll for completion
const pollStatus = async () => {
  const statusResponse = await fetch(`/status/${job_id}`);
  const status = await statusResponse.json();
  
  if (status.status === 'completed') {
    window.location.href = status.download_url;
  } else if (status.status === 'processing') {
    setTimeout(pollStatus, 1000);
  } else if (status.status === 'failed') {
    console.error('Compression failed:', status.error_details);
  }
};

pollStatus();
```

### 2. Advanced Compression with Options

```javascript
const compressWithOptions = async (file, options) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('image_quality', options.quality || 75);
  formData.append('image_scale', options.scale || 0.8);
  formData.append('skip_pages', options.skipPages || '');
  formData.append('method', options.method || 'auto');
  
  if (options.targetSize) {
    formData.append('target_size_mb', options.targetSize);
  }
  
  const response = await fetch('/compress', {
    method: 'POST',
    body: formData
  });
  
  return response.json();
};

// Usage
const result = await compressWithOptions(pdfFile, {
  quality: 60,
  scale: 0.7,
  skipPages: '1,10-15',
  method: 'pymupdf',
  targetSize: 5.0
});
```

### 3. Progress Tracking

```javascript
const trackProgress = (jobId, onProgress, onComplete, onError) => {
  const poll = async () => {
    try {
      const response = await fetch(`/status/${jobId}`);
      const status = await response.json();
      
      onProgress(status);
      
      if (status.status === 'completed') {
        onComplete(status);
      } else if (status.status === 'failed') {
        onError(status);
      } else if (status.status === 'processing') {
        setTimeout(poll, 1000);
      }
    } catch (error) {
      onError({ error_details: 'Network error' });
    }
  };
  
  poll();
};

// Usage
trackProgress(
  jobId,
  (status) => {
    console.log(`Progress: ${status.progress}% - ${status.message}`);
    updateProgressBar(status.progress);
  },
  (status) => {
    console.log('Compression completed!');
    showDownloadLink(status.download_url);
  },
  (status) => {
    console.error('Compression failed:', status.error_details);
    showError(status.error_details);
  }
);
```

## Rate Limiting

Currently no rate limiting is implemented. For production use, consider implementing:
- Request rate limiting per IP
- Concurrent job limits per user
- File size quotas

## File Cleanup

- Uploaded files are automatically deleted after processing
- Output files are cleaned up after 24 hours (configurable)
- Failed uploads are immediately cleaned up

## CORS Configuration

The service is configured to allow requests from:
- `https://platform.cofmodassets.net`
- `http://localhost:3000`
- `http://localhost:3001`
- `http://127.0.0.1:3000`

Additional origins can be configured in the FastAPI CORS middleware.
