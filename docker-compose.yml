version: '3.8'

services:
  pdf-compression-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      - UPLOAD_DIR=/app/uploads
      - OUTPUT_DIR=/app/outputs
      - MAX_FILE_SIZE=100
      - CLEANUP_INTERVAL_HOURS=24
      - BASE_URL=http://localhost:8000
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for production job queue (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes
  #   volumes:
  #     - redis_data:/data

# volumes:
#   redis_data:
