// Next.js 15 Integration Example for PDF Compression Service
// Save this as components/PDFCompressor.tsx in your Next.js project

'use client';

import React, { useState, useCallback } from 'react';

interface CompressionOptions {
  imageQuality: number;
  imageScale: number;
  skipPages: string;
  method: 'auto' | 'pymupdf' | 'ghostscript' | 'pdf2image';
  targetSizeMB?: number;
}

interface CompressionStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentPage?: number;
  totalPages?: number;
  message: string;
  downloadUrl?: string;
  originalSizeMB?: number;
  compressedSizeMB?: number;
  compressionRatio?: number;
  errorDetails?: string;
}

const PDFCompressor: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<CompressionStatus | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [options, setOptions] = useState<CompressionOptions>({
    imageQuality: 75,
    imageScale: 0.8,
    skipPages: '',
    method: 'auto',
  });

  // API base URL - update for your deployment
  const API_BASE_URL = process.env.NEXT_PUBLIC_PDF_API_URL || 'http://localhost:8000';

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === 'application/pdf') {
      setFile(selectedFile);
      setStatus(null);
    } else {
      alert('Please select a valid PDF file');
    }
  };

  const pollStatus = useCallback(async (jobId: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/status/${jobId}`);
      if (!response.ok) throw new Error('Failed to fetch status');
      
      const statusData: CompressionStatus = await response.json();
      setStatus(statusData);

      if (statusData.status === 'processing') {
        // Continue polling
        setTimeout(() => pollStatus(jobId), 1000);
      }
    } catch (error) {
      console.error('Error polling status:', error);
      setStatus(prev => prev ? {
        ...prev,
        status: 'failed',
        message: 'Failed to get status updates',
        errorDetails: 'Network error'
      } : null);
    }
  }, [API_BASE_URL]);

  const handleCompress = async () => {
    if (!file) return;

    setIsUploading(true);
    setStatus(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('image_quality', options.imageQuality.toString());
      formData.append('image_scale', options.imageScale.toString());
      formData.append('skip_pages', options.skipPages);
      formData.append('method', options.method);
      
      if (options.targetSizeMB) {
        formData.append('target_size_mb', options.targetSizeMB.toString());
      }

      const response = await fetch(`${API_BASE_URL}/compress`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload failed');
      }

      const result = await response.json();
      
      setStatus({
        jobId: result.job_id,
        status: result.status,
        progress: 0,
        message: result.message,
      });

      // Start polling for status updates
      pollStatus(result.job_id);

    } catch (error) {
      console.error('Error uploading file:', error);
      alert(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = () => {
    if (status?.downloadUrl) {
      window.open(status.downloadUrl, '_blank');
    }
  };

  const resetForm = () => {
    setFile(null);
    setStatus(null);
    setIsUploading(false);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">PDF Compressor</h2>
      
      {/* File Upload */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select PDF File
        </label>
        <input
          type="file"
          accept=".pdf"
          onChange={handleFileChange}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {file && (
          <p className="mt-2 text-sm text-gray-600">
            Selected: {file.name} ({(file.size / (1024 * 1024)).toFixed(2)} MB)
          </p>
        )}
      </div>

      {/* Compression Options */}
      <div className="mb-6 space-y-4">
        <h3 className="text-lg font-semibold text-gray-700">Compression Options</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Quality (1-100)
            </label>
            <input
              type="number"
              min="1"
              max="100"
              value={options.imageQuality}
              onChange={(e) => setOptions(prev => ({ ...prev, imageQuality: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Scale (0.1-1.0)
            </label>
            <input
              type="number"
              min="0.1"
              max="1.0"
              step="0.1"
              value={options.imageScale}
              onChange={(e) => setOptions(prev => ({ ...prev, imageScale: parseFloat(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Skip Pages (e.g., "1,5,10-12")
          </label>
          <input
            type="text"
            value={options.skipPages}
            onChange={(e) => setOptions(prev => ({ ...prev, skipPages: e.target.value }))}
            placeholder="1,5,10-12"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Compression Method
          </label>
          <select
            value={options.method}
            onChange={(e) => setOptions(prev => ({ ...prev, method: e.target.value as CompressionOptions['method'] }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="auto">Auto (Recommended)</option>
            <option value="pymupdf">PyMuPDF</option>
            <option value="ghostscript">Ghostscript</option>
            <option value="pdf2image">PDF2Image</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Target Size (MB, optional)
          </label>
          <input
            type="number"
            min="0.1"
            step="0.1"
            value={options.targetSizeMB || ''}
            onChange={(e) => setOptions(prev => ({ 
              ...prev, 
              targetSizeMB: e.target.value ? parseFloat(e.target.value) : undefined 
            }))}
            placeholder="5.0"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mb-6 flex space-x-4">
        <button
          onClick={handleCompress}
          disabled={!file || isUploading || (status?.status === 'processing')}
          className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isUploading ? 'Uploading...' : 'Compress PDF'}
        </button>
        
        <button
          onClick={resetForm}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Reset
        </button>
      </div>

      {/* Status Display */}
      {status && (
        <div className="bg-gray-50 p-4 rounded-md">
          <h4 className="font-semibold text-gray-800 mb-2">Compression Status</h4>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Status:</span>
              <span className={`font-semibold ${
                status.status === 'completed' ? 'text-green-600' :
                status.status === 'failed' ? 'text-red-600' :
                status.status === 'processing' ? 'text-blue-600' :
                'text-yellow-600'
              }`}>
                {status.status.toUpperCase()}
              </span>
            </div>
            
            {status.progress > 0 && (
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Progress:</span>
                  <span>{status.progress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${status.progress}%` }}
                  ></div>
                </div>
              </div>
            )}
            
            {status.currentPage && status.totalPages && (
              <div className="flex justify-between">
                <span>Page:</span>
                <span>{status.currentPage} / {status.totalPages}</span>
              </div>
            )}
            
            <div className="text-sm text-gray-600">
              {status.message}
            </div>
            
            {status.originalSizeMB && status.compressedSizeMB && (
              <div className="mt-3 p-3 bg-green-50 rounded border">
                <div className="text-sm space-y-1">
                  <div>Original: {status.originalSizeMB.toFixed(2)} MB</div>
                  <div>Compressed: {status.compressedSizeMB.toFixed(2)} MB</div>
                  <div>Savings: {status.compressionRatio?.toFixed(1)}%</div>
                </div>
              </div>
            )}
            
            {status.status === 'completed' && status.downloadUrl && (
              <button
                onClick={handleDownload}
                className="w-full mt-3 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
              >
                Download Compressed PDF
              </button>
            )}
            
            {status.status === 'failed' && status.errorDetails && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                <div className="text-sm text-red-700">
                  Error: {status.errorDetails}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFCompressor;
