import os
import fitz  # PyMuPDF
from PIL import Image
import io
import subprocess
import sys
from typing import Set, Optional, Callable
from utils import get_file_size_mb


class CompressionProgress:
    """Class to track and report compression progress."""
    
    def __init__(self, total_pages: int, progress_callback: Optional[Callable] = None):
        self.total_pages = total_pages
        self.current_page = 0
        self.progress_callback = progress_callback
    
    def update(self, page_num: int, message: str = ""):
        """Update progress and call callback if provided."""
        self.current_page = page_num
        progress_percent = (page_num / self.total_pages) * 100 if self.total_pages > 0 else 0
        
        if self.progress_callback:
            self.progress_callback({
                "current_page": page_num,
                "total_pages": self.total_pages,
                "progress": progress_percent,
                "message": message
            })


def compress_pdf_pymupdf(input_path: str, output_path: str, image_quality: int = 75, 
                        image_scale: float = 0.8, skip_pages: Set[int] = None,
                        progress_callback: Optional[Callable] = None) -> dict:
    """
    Compress PDF using PyMuPDF by reducing image quality while preserving document structure.
    
    Args:
        input_path: Path to input PDF
        output_path: Path to output PDF
        image_quality: JPEG quality (1-100)
        image_scale: Scale factor for large images
        skip_pages: Set of page numbers to skip (1-based indexing)
        progress_callback: Function to call with progress updates
        
    Returns:
        Dict with compression results
    """
    if skip_pages is None:
        skip_pages = set()
    
    try:
        # Open the PDF
        pdf_document = fitz.open(input_path)
        total_pages = len(pdf_document)
        
        # Initialize progress tracker
        progress = CompressionProgress(total_pages, progress_callback)
        
        # Get initial file size
        initial_size = get_file_size_mb(input_path)
        
        # Process each page
        for page_num in range(total_pages):
            page_display_num = page_num + 1  # Human-readable page number
            
            # Update progress
            progress.update(page_display_num, f"Processing page {page_display_num}/{total_pages}")
            
            # Check if this page should be skipped
            if page_display_num in skip_pages:
                continue
            
            page = pdf_document[page_num]
            
            # Get list of images on this page
            image_list = page.get_images()
            
            # Process each image
            for img_index, img in enumerate(image_list):
                try:
                    # Extract image
                    xref = img[0]
                    
                    # Check if image exists and can be extracted
                    if xref == 0:
                        continue
                    
                    pix = fitz.Pixmap(pdf_document, xref)
                    
                    # Skip if pixmap is invalid
                    if pix.width <= 0 or pix.height <= 0:
                        pix = None
                        continue
                    
                    # Convert to PIL Image
                    img_data = pix.tobytes()
                    img_pil = Image.open(io.BytesIO(img_data))
                    
                    # Convert RGBA to RGB if necessary
                    if img_pil.mode == "RGBA":
                        rgb_img = Image.new("RGB", img_pil.size, (255, 255, 255))
                        rgb_img.paste(img_pil, mask=img_pil.split()[3])
                        img_pil = rgb_img
                    elif img_pil.mode not in ["RGB", "L"]:
                        img_pil = img_pil.convert("RGB")
                    
                    # Scale down large images
                    if img_pil.width > 1500 or img_pil.height > 1500:
                        new_width = int(img_pil.width * image_scale)
                        new_height = int(img_pil.height * image_scale)
                        if new_width > 0 and new_height > 0:
                            img_pil = img_pil.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    
                    # Compress image
                    img_buffer = io.BytesIO()
                    img_pil.save(img_buffer, format='JPEG', quality=image_quality, optimize=True)
                    img_data = img_buffer.getvalue()
                    
                    # Replace image in PDF
                    pdf_document.xref_set_key(xref, "Filter", "[/DCTDecode]")
                    pdf_document.xref_stream(xref, img_data)
                    
                    # Clean up
                    pix = None
                    img_buffer.close()
                    
                except Exception as e:
                    # Skip problematic images
                    continue
        
        # Save with garbage collection and compression
        pdf_document.save(output_path, garbage=4, deflate=True, clean=True)
        pdf_document.close()
        
        # Check final size
        final_size = get_file_size_mb(output_path)
        compression_ratio = (1 - final_size/initial_size) * 100 if initial_size > 0 else 0
        
        # Final progress update
        progress.update(total_pages, "Compression completed")
        
        return {
            "success": True,
            "initial_size_mb": initial_size,
            "final_size_mb": final_size,
            "compression_ratio": compression_ratio,
            "method": "pymupdf"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "method": "pymupdf"
        }


def compress_pdf_ghostscript(input_path: str, output_path: str, quality_setting: str = "/ebook",
                           progress_callback: Optional[Callable] = None) -> dict:
    """
    Compress PDF using Ghostscript.
    
    Args:
        input_path: Path to input PDF
        output_path: Path to output PDF
        quality_setting: Ghostscript quality setting (/screen, /ebook, /printer, /prepress)
        progress_callback: Function to call with progress updates
        
    Returns:
        Dict with compression results
    """
    try:
        if progress_callback:
            progress_callback({
                "current_page": 0,
                "total_pages": 1,
                "progress": 0,
                "message": "Starting Ghostscript compression"
            })
        
        # Determine Ghostscript executable based on OS
        if sys.platform == "win32":
            gs_cmd = "gswin64c"  # or "gswin32c" for 32-bit
        else:
            gs_cmd = "gs"
        
        gs_command = [
            gs_cmd,
            "-sDEVICE=pdfwrite",
            "-dCompatibilityLevel=1.4",
            f"-dPDFSETTINGS={quality_setting}",
            "-dNOPAUSE",
            "-dBATCH",
            "-dQUIET",
            "-dDownsampleColorImages=true",
            "-dDownsampleGrayImages=true",
            "-dColorImageResolution=150",
            "-dGrayImageResolution=150",
            "-dColorImageDownsampleType=/Bicubic",
            "-dGrayImageDownsampleType=/Bicubic",
            "-dColorConversionStrategy=/RGB",
            "-dAutoRotatePages=/None",
            "-dEmbedAllFonts=true",
            "-dSubsetFonts=true",
            f"-sOutputFile={output_path}",
            input_path
        ]
        
        if progress_callback:
            progress_callback({
                "current_page": 0,
                "total_pages": 1,
                "progress": 50,
                "message": "Running Ghostscript compression"
            })
        
        # Run Ghostscript
        subprocess.run(gs_command, check=True, capture_output=True)
        
        # Check file sizes
        initial_size = get_file_size_mb(input_path)
        final_size = get_file_size_mb(output_path)
        compression_ratio = (1 - final_size/initial_size) * 100 if initial_size > 0 else 0
        
        if progress_callback:
            progress_callback({
                "current_page": 1,
                "total_pages": 1,
                "progress": 100,
                "message": "Ghostscript compression completed"
            })
        
        return {
            "success": True,
            "initial_size_mb": initial_size,
            "final_size_mb": final_size,
            "compression_ratio": compression_ratio,
            "method": "ghostscript"
        }
        
    except subprocess.CalledProcessError as e:
        return {
            "success": False,
            "error": f"Ghostscript compression failed: {e}",
            "method": "ghostscript"
        }
    except FileNotFoundError:
        return {
            "success": False,
            "error": "Ghostscript not found. Please install Ghostscript.",
            "method": "ghostscript"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "method": "ghostscript"
        }


def compress_pdf_pdf2image(input_path: str, output_path: str, dpi: int = 150,
                          quality: int = 85, skip_pages: Set[int] = None,
                          progress_callback: Optional[Callable] = None) -> dict:
    """
    Compress PDF by converting to images and back.

    Args:
        input_path: Path to input PDF
        output_path: Path to output PDF
        dpi: DPI for image conversion
        quality: JPEG quality for compression
        skip_pages: Set of page numbers to skip (1-based indexing)
        progress_callback: Function to call with progress updates

    Returns:
        Dict with compression results
    """
    if skip_pages is None:
        skip_pages = set()

    try:
        from pdf2image import convert_from_path
        import img2pdf

        if progress_callback:
            progress_callback({
                "current_page": 0,
                "total_pages": 1,
                "progress": 0,
                "message": "Converting PDF to images"
            })

        # Convert PDF pages to images
        images = convert_from_path(input_path, dpi=dpi)
        total_pages = len(images)

        # Compress each image
        compressed_images = []
        for i, img in enumerate(images):
            page_num = i + 1

            if progress_callback:
                progress_callback({
                    "current_page": page_num,
                    "total_pages": total_pages,
                    "progress": (page_num / total_pages) * 90,  # Reserve 10% for final conversion
                    "message": f"Processing page {page_num}/{total_pages}"
                })

            if page_num in skip_pages:
                # Keep original quality for skipped pages
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='JPEG', quality=95, optimize=False)
                compressed_images.append(img_buffer.getvalue())
            else:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Save to bytes with compression
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='JPEG', quality=quality, optimize=True)
                compressed_images.append(img_buffer.getvalue())

        if progress_callback:
            progress_callback({
                "current_page": total_pages,
                "total_pages": total_pages,
                "progress": 95,
                "message": "Rebuilding PDF"
            })

        # Convert back to PDF
        with open(output_path, "wb") as f:
            f.write(img2pdf.convert(compressed_images))

        # Check results
        initial_size = get_file_size_mb(input_path)
        final_size = get_file_size_mb(output_path)
        compression_ratio = (1 - final_size/initial_size) * 100 if initial_size > 0 else 0

        if progress_callback:
            progress_callback({
                "current_page": total_pages,
                "total_pages": total_pages,
                "progress": 100,
                "message": "PDF2Image compression completed"
            })

        return {
            "success": True,
            "initial_size_mb": initial_size,
            "final_size_mb": final_size,
            "compression_ratio": compression_ratio,
            "method": "pdf2image"
        }

    except ImportError:
        return {
            "success": False,
            "error": "pdf2image not installed. Install with: pip install pdf2image img2pdf",
            "method": "pdf2image"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "method": "pdf2image"
        }


def compress_pdf_auto(input_path: str, output_path: str, image_quality: int = 75,
                     image_scale: float = 0.8, skip_pages: Set[int] = None,
                     target_size_mb: Optional[float] = None,
                     progress_callback: Optional[Callable] = None) -> dict:
    """
    Automatically choose the best compression method and settings.

    Args:
        input_path: Path to input PDF
        output_path: Path to output PDF
        image_quality: Initial JPEG quality (1-100)
        image_scale: Scale factor for large images
        skip_pages: Set of page numbers to skip (1-based indexing)
        target_size_mb: Target file size in MB (optional)
        progress_callback: Function to call with progress updates

    Returns:
        Dict with compression results
    """
    # Get initial size for reference
    initial_size = get_file_size_mb(input_path)

    # Try PyMuPDF first (most reliable)
    if progress_callback:
        progress_callback({
            "current_page": 0,
            "total_pages": 1,
            "progress": 0,
            "message": "Trying PyMuPDF compression"
        })

    quality = image_quality
    while quality >= 30:
        temp_output = output_path.replace('.pdf', f'_temp_{quality}.pdf')

        result = compress_pdf_pymupdf(
            input_path, temp_output, quality, image_scale, skip_pages, progress_callback
        )

        if result["success"]:
            final_size = result["final_size_mb"]

            # Check if target size is met or if it's good enough
            if target_size_mb is None or final_size <= target_size_mb * 1.05:  # 5% tolerance
                os.rename(temp_output, output_path)
                return result
            else:
                os.remove(temp_output)
                quality -= 15
        else:
            break

    # Try Ghostscript if PyMuPDF didn't work well enough
    if progress_callback:
        progress_callback({
            "current_page": 0,
            "total_pages": 1,
            "progress": 50,
            "message": "Trying Ghostscript compression"
        })

    temp_output = output_path.replace('.pdf', '_gs_temp.pdf')
    result = compress_pdf_ghostscript(input_path, temp_output, "/ebook", progress_callback)

    if result["success"]:
        final_size = result["final_size_mb"]

        if target_size_mb is None or final_size <= target_size_mb * 1.05:
            os.rename(temp_output, output_path)
            return result
        else:
            os.remove(temp_output)
            # Try more aggressive Ghostscript compression
            result = compress_pdf_ghostscript(input_path, output_path, "/screen", progress_callback)
            if result["success"]:
                return result

    # Try pdf2image as last resort
    if progress_callback:
        progress_callback({
            "current_page": 0,
            "total_pages": 1,
            "progress": 75,
            "message": "Trying PDF2Image compression"
        })

    result = compress_pdf_pdf2image(input_path, output_path, 150, 80, skip_pages, progress_callback)

    if result["success"]:
        return result

    # If all methods failed, return the last error
    return {
        "success": False,
        "error": "All compression methods failed",
        "method": "auto"
    }
